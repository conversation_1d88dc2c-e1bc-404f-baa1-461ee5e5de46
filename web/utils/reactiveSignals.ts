import { useEffect, useState, useCallback } from 'react';

// Simple reactive signal implementation
export class Signal<T> {
  private _value: T;
  private _listeners = new Set<(value: T) => void>();

  constructor(initialValue: T) {
    this._value = initialValue;
  }

  get value(): T {
    return this._value;
  }

  set value(newValue: T) {
    if (this._value !== newValue) {
      this._value = newValue;
      this._listeners.forEach(listener => listener(newValue));
    }
  }

  subscribe(listener: (value: T) => void): () => void {
    this._listeners.add(listener);
    return () => this._listeners.delete(listener);
  }

  update(updater: (current: T) => T): void {
    this.value = updater(this._value);
  }
}

// Computed signal that derives from other signals
export class ComputedSignal<T> {
  private _value: T;
  private _listeners = new Set<(value: T) => void>();
  private _dependencies: Signal<any>[];
  private _compute: () => T;
  private _unsubscribers: (() => void)[] = [];

  constructor(compute: () => T, dependencies: Signal<any>[]) {
    this._compute = compute;
    this._dependencies = dependencies;
    this._value = compute();

    // Subscribe to all dependencies
    this._unsubscribers = dependencies.map(dep =>
      dep.subscribe(() => {
        const newValue = this._compute();
        if (this._value !== newValue) {
          this._value = newValue;
          this._listeners.forEach(listener => listener(newValue));
        }
      })
    );
  }

  get value(): T {
    return this._value;
  }

  subscribe(listener: (value: T) => void): () => void {
    this._listeners.add(listener);
    return () => this._listeners.delete(listener);
  }

  destroy(): void {
    this._unsubscribers.forEach(unsub => unsub());
    this._listeners.clear();
  }
}

// React hook to use signals
export function useSignal<T>(signal: Signal<T>): [T, (value: T) => void] {
  const [value, setValue] = useState(signal.value);

  useEffect(() => {
    const unsubscribe = signal.subscribe(setValue);
    return unsubscribe;
  }, [signal]);

  const updateSignal = useCallback((newValue: T) => {
    signal.value = newValue;
  }, [signal]);

  return [value, updateSignal];
}

// React hook for computed signals
export function useComputedSignal<T>(computedSignal: ComputedSignal<T>): T {
  const [value, setValue] = useState(computedSignal.value);

  useEffect(() => {
    const unsubscribe = computedSignal.subscribe(setValue);
    return unsubscribe;
  }, [computedSignal]);

  return value;
}

// Step navigation using signals
export class StepNavigationSignals<T extends number> {
  public currentStep: Signal<T>;
  public totalSteps: Signal<number>;
  public isFirstStep: ComputedSignal<boolean>;
  public isLastStep: ComputedSignal<boolean>;
  
  private validationFn?: (current: T, target: T) => Promise<boolean> | boolean;
  private onStepChangeFn?: (newStep: T, prevStep: T) => void;
  private stepParam: string;
  private syncWithURL: boolean;

  constructor(config: {
    initialStep: T;
    totalSteps: number;
    stepParam?: string;
    syncWithURL?: boolean;
    onStepValidation?: (current: T, target: T) => Promise<boolean> | boolean;
    onStepChange?: (newStep: T, prevStep: T) => void;
  }) {
    this.currentStep = new Signal(config.initialStep);
    this.totalSteps = new Signal(config.totalSteps);
    this.stepParam = config.stepParam || 'step';
    this.syncWithURL = config.syncWithURL ?? true;
    this.validationFn = config.onStepValidation;
    this.onStepChangeFn = config.onStepChange;

    this.isFirstStep = new ComputedSignal(
      () => this.currentStep.value === 1,
      [this.currentStep]
    );

    this.isLastStep = new ComputedSignal(
      () => this.currentStep.value === this.totalSteps.value,
      [this.currentStep, this.totalSteps]
    );

    // Subscribe to step changes for URL sync and callbacks
    this.currentStep.subscribe((newStep) => {
      if (this.syncWithURL) {
        this.updateURL(newStep);
      }
    });
  }

  private updateURL(step: T): void {
    if (typeof window === 'undefined') return;
    
    const url = new URL(window.location.href);
    url.searchParams.set(this.stepParam, step.toString());
    window.history.pushState({ step }, '', url.toString());
  }

  async goNext(): Promise<void> {
    const current = this.currentStep.value;
    const total = this.totalSteps.value;
    
    if (current >= total) return;

    const next = (current + 1) as T;
    
    if (this.validationFn) {
      const isValid = await this.validationFn(current, next);
      if (!isValid) return;
    }

    const prev = current;
    this.currentStep.value = next;
    this.onStepChangeFn?.(next, prev);
  }

  goBack(): void {
    const current = this.currentStep.value;
    if (current <= 1) return;
    
    const prev = current;
    const newStep = (current - 1) as T;
    this.currentStep.value = newStep;
    this.onStepChangeFn?.(newStep, prev);
  }

  async goToStep(step: T): Promise<void> {
    const current = this.currentStep.value;
    const total = this.totalSteps.value;
    
    if (step < 1 || step > total || step === current) return;

    if (this.validationFn) {
      const isValid = await this.validationFn(current, step);
      if (!isValid) return;
    }

    const prev = current;
    this.currentStep.value = step;
    this.onStepChangeFn?.(step, prev);
  }

  isStepCompleted(step: number): boolean {
    return step < this.currentStep.value;
  }

  isStepActive(step: number): boolean {
    return step === this.currentStep.value;
  }

  destroy(): void {
    this.isFirstStep.destroy();
    this.isLastStep.destroy();
  }
}

// React hook for step navigation signals
export function useStepNavigationSignals<T extends number>(
  stepNavigation: StepNavigationSignals<T>
) {
  const [currentStep] = useSignal(stepNavigation.currentStep);
  const isFirstStep = useComputedSignal(stepNavigation.isFirstStep);
  const isLastStep = useComputedSignal(stepNavigation.isLastStep);

  return {
    currentStep,
    isFirstStep,
    isLastStep,
    goNext: () => stepNavigation.goNext(),
    goBack: () => stepNavigation.goBack(),
    goToStep: (step: T) => stepNavigation.goToStep(step),
    isStepCompleted: (step: number) => stepNavigation.isStepCompleted(step),
    isStepActive: (step: number) => stepNavigation.isStepActive(step),
  };
}
