import { atom } from 'jotai';
import { atomWithStorage } from 'jotai/utils';

// Base atoms
export const currentStepAtom = atom<number>(1);
export const totalStepsAtom = atom<number>(3);
export const stepParamAtom = atom<string>('step');
export const syncWithURLAtom = atom<boolean>(true);

// Derived atoms
export const isFirstStepAtom = atom((get) => get(currentStepAtom) === 1);
export const isLastStepAtom = atom((get) => get(currentStepAtom) === get(totalStepsAtom));

export const isStepCompletedAtom = atom(null, (get, set, step: number) => {
  return step < get(currentStepAtom);
});

export const isStepActiveAtom = atom(null, (get, set, step: number) => {
  return step === get(currentStepAtom);
});

// URL sync atom
export const urlSyncAtom = atom(
  null,
  (get, set, step: number) => {
    if (typeof window === 'undefined' || !get(syncWithURLAtom)) return;
    
    const url = new URL(window.location.href);
    url.searchParams.set(get(stepParamAtom), step.toString());
    window.history.pushState({ step }, '', url.toString());
  }
);

// Navigation actions
export const goNextAtom = atom(
  null,
  async (get, set, validationFn?: (current: number, target: number) => Promise<boolean> | boolean) => {
    const current = get(currentStepAtom);
    const total = get(totalStepsAtom);
    
    if (current >= total) return;
    
    const next = current + 1;
    
    if (validationFn) {
      const isValid = await validationFn(current, next);
      if (!isValid) return;
    }
    
    set(currentStepAtom, next);
    set(urlSyncAtom, next);
  }
);

export const goBackAtom = atom(
  null,
  (get, set) => {
    const current = get(currentStepAtom);
    if (current <= 1) return;
    
    const prev = current - 1;
    set(currentStepAtom, prev);
    set(urlSyncAtom, prev);
  }
);

export const goToStepAtom = atom(
  null,
  async (get, set, step: number, validationFn?: (current: number, target: number) => Promise<boolean> | boolean) => {
    const current = get(currentStepAtom);
    const total = get(totalStepsAtom);
    
    if (step < 1 || step > total || step === current) return;
    
    if (validationFn) {
      const isValid = await validationFn(current, step);
      if (!isValid) return;
    }
    
    set(currentStepAtom, step);
    set(urlSyncAtom, step);
  }
);

// Factory function to create step navigation atoms for specific features
export function createStepNavigationAtoms(config: {
  initialStep: number;
  totalSteps: number;
  stepParam?: string;
  syncWithURL?: boolean;
}) {
  const currentStep = atom(config.initialStep);
  const totalSteps = atom(config.totalSteps);
  const stepParam = atom(config.stepParam || 'step');
  const syncWithURL = atom(config.syncWithURL ?? true);
  
  return {
    currentStep,
    totalSteps,
    stepParam,
    syncWithURL,
    isFirstStep: atom((get) => get(currentStep) === 1),
    isLastStep: atom((get) => get(currentStep) === get(totalSteps)),
    // ... other derived atoms
  };
}

// Usage example:
// const jobMatchSteps = createStepNavigationAtoms({
//   initialStep: 1,
//   totalSteps: 3,
//   stepParam: 'job-match-step'
// });
