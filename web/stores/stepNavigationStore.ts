import { create } from 'zustand';
import { subscribeWithSelector } from 'zustand/middleware';

export interface StepNavigationState<T extends number> {
  currentStep: T;
  totalSteps: number;
  stepParam: string;
  syncWithURL: boolean;
  isFirstStep: boolean;
  isLastStep: boolean;
  
  // Actions
  goNext: () => Promise<void>;
  goBack: () => void;
  goToStep: (step: T) => Promise<void>;
  setCurrentStep: (step: T) => void;
  isStepCompleted: (step: number) => boolean;
  isStepActive: (step: number) => boolean;
  
  // Configuration
  setValidation: (fn: (current: T, target: T) => Promise<boolean> | boolean) => void;
  setOnStepChange: (fn: (newStep: T, prevStep: T) => void) => void;
}

export interface StepNavigationConfig<T extends number> {
  initialStep: T;
  totalSteps: number;
  stepParam?: string;
  syncWithURL?: boolean;
  onStepValidation?: (currentStep: T, targetStep: T) => Promise<boolean> | boolean;
  onStepChange?: (newStep: T, prevStep: T) => void;
}

export function createStepNavigationStore<T extends number>(config: StepNavigationConfig<T>) {
  return create<StepNavigationState<T>>()(
    subscribeWithSelector((set, get) => {
      let validationFn = config.onStepValidation;
      let onStepChangeFn = config.onStepChange;
      
      const updateURL = (step: T) => {
        if (typeof window === 'undefined' || !get().syncWithURL) return;
        
        const url = new URL(window.location.href);
        url.searchParams.set(get().stepParam, step.toString());
        window.history.pushState({ step }, '', url.toString());
      };

      const store: StepNavigationState<T> = {
        currentStep: config.initialStep,
        totalSteps: config.totalSteps,
        stepParam: config.stepParam || 'step',
        syncWithURL: config.syncWithURL ?? true,
        
        get isFirstStep() {
          return get().currentStep === 1;
        },
        
        get isLastStep() {
          return get().currentStep === get().totalSteps;
        },

        setCurrentStep: (step: T) => {
          const prevStep = get().currentStep;
          set({ currentStep: step });
          updateURL(step);
          onStepChangeFn?.(step, prevStep);
        },

        goNext: async () => {
          const { currentStep, totalSteps } = get();
          if (currentStep >= totalSteps) return;

          const nextStep = (currentStep + 1) as T;
          
          if (validationFn) {
            const isValid = await validationFn(currentStep, nextStep);
            if (!isValid) return;
          }

          get().setCurrentStep(nextStep);
        },

        goBack: () => {
          const { currentStep } = get();
          if (currentStep <= 1) return;
          
          const prevStep = (currentStep - 1) as T;
          get().setCurrentStep(prevStep);
        },

        goToStep: async (step: T) => {
          const { currentStep, totalSteps } = get();
          if (step < 1 || step > totalSteps || step === currentStep) return;

          if (validationFn) {
            const isValid = await validationFn(currentStep, step);
            if (!isValid) return;
          }

          get().setCurrentStep(step);
        },

        isStepCompleted: (step: number) => step < get().currentStep,
        isStepActive: (step: number) => step === get().currentStep,
        
        setValidation: (fn) => {
          validationFn = fn;
        },
        
        setOnStepChange: (fn) => {
          onStepChangeFn = fn;
        },
      };

      return store;
    })
  );
}

// Usage example:
// const useJobMatchSteps = createStepNavigationStore({
//   initialStep: 1 as 1 | 2 | 3,
//   totalSteps: 3,
//   stepParam: 'job-match-step',
//   onStepValidation: async (current, target) => {
//     // validation logic
//     return true;
//   }
// });
