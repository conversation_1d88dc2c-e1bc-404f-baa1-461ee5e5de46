'use client';

import useS<PERSON>, { mutate } from 'swr';
import { useCallback, useEffect } from 'react';

export interface ReactiveStepConfig<T extends number> {
  key: string; // Unique key for this step navigation instance
  initialStep: T;
  totalSteps: number;
  stepParam?: string;
  syncWithURL?: boolean;
  onStepValidation?: (currentStep: T, targetStep: T) => Promise<boolean> | boolean;
  onStepChange?: (newStep: T, prevStep: T) => void;
}

interface StepNavigationData<T extends number> {
  currentStep: T;
  totalSteps: number;
  stepParam: string;
  syncWithURL: boolean;
}

export function useReactiveStepNavigation<T extends number>({
  key,
  initialStep,
  totalSteps,
  stepParam = 'step',
  syncWithURL = true,
  onStepValidation,
  onStepChange,
}: ReactiveStepConfig<T>) {
  
  // Use SWR to manage step state reactively
  const { data: stepData, mutate: mutateStep } = useSWR<StepNavigationData<T>>(
    `step-navigation-${key}`,
    null,
    {
      fallbackData: {
        currentStep: initialStep,
        totalSteps,
        stepParam,
        syncWithURL,
      },
      revalidateOnFocus: false,
      revalidateOnReconnect: false,
    }
  );

  const currentStep = stepData!.currentStep;

  // URL sync effect
  useEffect(() => {
    if (typeof window === 'undefined' || !syncWithURL) return;

    // Remove step parameter on mount for fresh start
    const urlParams = new URLSearchParams(window.location.search);
    if (urlParams.has(stepParam)) {
      urlParams.delete(stepParam);
      const newUrl = new URL(window.location.href);
      newUrl.search = urlParams.toString();
      window.history.replaceState(null, '', newUrl.toString());
    }
  }, []);

  // Browser navigation handler
  useEffect(() => {
    if (typeof window === 'undefined' || !syncWithURL) return;

    const handlePopState = () => {
      const urlParams = new URLSearchParams(window.location.search);
      const stepFromURL = urlParams.get(stepParam);
      
      if (stepFromURL) {
        const parsedStep = parseInt(stepFromURL, 10);
        if (parsedStep >= 1 && parsedStep <= totalSteps) {
          const newStep = parsedStep as T;
          const prevStep = currentStep;
          
          mutateStep({
            ...stepData!,
            currentStep: newStep,
          }, false);
          
          onStepChange?.(newStep, prevStep);
        }
      } else {
        const prevStep = currentStep;
        mutateStep({
          ...stepData!,
          currentStep: initialStep,
        }, false);
        
        onStepChange?.(initialStep, prevStep);
      }
    };

    window.addEventListener('popstate', handlePopState);
    return () => window.removeEventListener('popstate', handlePopState);
  }, [currentStep, initialStep, totalSteps, stepParam, syncWithURL, onStepChange, stepData, mutateStep]);

  // Update URL helper
  const updateURL = useCallback((step: T) => {
    if (typeof window === 'undefined' || !syncWithURL) return;
    
    const url = new URL(window.location.href);
    url.searchParams.set(stepParam, step.toString());
    window.history.pushState({ step }, '', url.toString());
  }, [stepParam, syncWithURL]);

  // Navigation actions
  const goNext = useCallback(async () => {
    if (currentStep >= totalSteps) return;

    const nextStep = (currentStep + 1) as T;
    
    if (onStepValidation) {
      const isValid = await onStepValidation(currentStep, nextStep);
      if (!isValid) return;
    }

    const prevStep = currentStep;
    await mutateStep({
      ...stepData!,
      currentStep: nextStep,
    }, false);
    
    updateURL(nextStep);
    onStepChange?.(nextStep, prevStep);
  }, [currentStep, totalSteps, onStepValidation, stepData, mutateStep, updateURL, onStepChange]);

  const goBack = useCallback(async () => {
    if (currentStep <= 1) return;

    const prevStepNumber = (currentStep - 1) as T;
    const prevStep = currentStep;
    
    await mutateStep({
      ...stepData!,
      currentStep: prevStepNumber,
    }, false);
    
    updateURL(prevStepNumber);
    onStepChange?.(prevStepNumber, prevStep);
  }, [currentStep, stepData, mutateStep, updateURL, onStepChange]);

  const goToStep = useCallback(async (step: T) => {
    if (step < 1 || step > totalSteps || step === currentStep) return;

    if (onStepValidation) {
      const isValid = await onStepValidation(currentStep, step);
      if (!isValid) return;
    }

    const prevStep = currentStep;
    await mutateStep({
      ...stepData!,
      currentStep: step,
    }, false);
    
    updateURL(step);
    onStepChange?.(step, prevStep);
  }, [currentStep, totalSteps, onStepValidation, stepData, mutateStep, updateURL, onStepChange]);

  // Helper functions
  const isFirstStep = currentStep === 1;
  const isLastStep = currentStep === totalSteps;
  const isStepCompleted = useCallback((step: number) => step < currentStep, [currentStep]);
  const isStepActive = useCallback((step: number) => step === currentStep, [currentStep]);

  return {
    currentStep,
    goNext,
    goBack,
    goToStep,
    isFirstStep,
    isLastStep,
    isStepCompleted,
    isStepActive,
  };
}

// Usage example:
// const stepNav = useReactiveStepNavigation({
//   key: 'job-match',
//   initialStep: 1 as 1 | 2 | 3,
//   totalSteps: 3,
//   stepParam: 'job-match-step',
//   onStepValidation: async (current, target) => {
//     return !!resumeUploaded;
//   }
// });
